# DSA Java - Modular Workflow Guide

## 🎯 **Project Overview**
This project is structured for creating and running individual DSA programs independently. Each program can be compiled and executed separately using Maven commands.

## 📁 **Directory Structure**
```
dsa-java/
├── src/main/java/com/dsa/practice/
│   ├── arrays/              # Array problems & solutions
│   ├── linkedlists/         # Linked list implementations  
│   ├── stacks/              # Stack data structure & problems
│   ├── queues/              # Queue data structure & problems
│   ├── trees/               # Tree structures (Binary, BST, etc.)
│   ├── graphs/              # Graph algorithms & implementations
│   ├── sorting/             # Sorting algorithms
│   ├── searching/           # Searching algorithms
│   ├── dynamicprogramming/  # DP problems & solutions
│   ├── greedy/              # Greedy algorithm problems
│   ├── backtracking/        # Backtracking problems
│   └── utils/               # Utility classes & helpers
└── src/test/java/com/dsa/practice/  # Corresponding test files
```

## 🚀 **Workflow: Creating Individual Programs**

### **Step 1: Create a New Program**
Each program should be a standalone class with a `main` method in the appropriate topic folder.

**Example: Creating TwoSum.java in arrays folder**
```java
package com.dsa.practice.arrays;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Two Sum Problem
 * Given an array of integers and a target, find two numbers that add up to target.
 */
public class TwoSum {
    
    public static int[] twoSum(int[] nums, int target) {
        Map<Integer, Integer> map = new HashMap<>();
        
        for (int i = 0; i < nums.length; i++) {
            int complement = target - nums[i];
            if (map.containsKey(complement)) {
                return new int[]{map.get(complement), i};
            }
            map.put(nums[i], i);
        }
        
        return new int[]{};
    }
    
    public static void main(String[] args) {
        // Test cases
        int[] nums1 = {2, 7, 11, 15};
        int target1 = 9;
        System.out.println("Input: " + Arrays.toString(nums1) + ", Target: " + target1);
        System.out.println("Output: " + Arrays.toString(twoSum(nums1, target1)));
        
        int[] nums2 = {3, 2, 4};
        int target2 = 6;
        System.out.println("Input: " + Arrays.toString(nums2) + ", Target: " + target2);
        System.out.println("Output: " + Arrays.toString(twoSum(nums2, target2)));
    }
}
```

### **Step 2: Maven Commands for Individual Programs**

#### **Compile Specific Program**
```bash
# Compile all programs
mvn compile

# Compile and run specific program
mvn compile exec:java -Dexec.mainClass="com.dsa.practice.arrays.TwoSum"
```

#### **Run Specific Program**
```bash
# Run TwoSum program
mvn exec:java -Dexec.mainClass="com.dsa.practice.arrays.TwoSum"

# Run with arguments (if your program accepts command line args)
mvn exec:java -Dexec.mainClass="com.dsa.practice.arrays.TwoSum" -Dexec.args="arg1 arg2"
```

#### **Quick Compile and Run**
```bash
# One-liner to compile and run
mvn compile exec:java -Dexec.mainClass="com.dsa.practice.arrays.TwoSum"
```

## 📋 **Program Template**

### **Basic Template for New Programs**
```java
package com.dsa.practice.[TOPIC];  // Replace [TOPIC] with folder name

/**
 * [Problem Name]
 * [Problem Description]
 * 
 * Time Complexity: O(?)
 * Space Complexity: O(?)
 */
public class [ClassName] {
    
    // Your solution method(s) here
    public static [returnType] [methodName]([parameters]) {
        // Implementation
    }
    
    public static void main(String[] args) {
        // Test cases and examples
        System.out.println("=== [Problem Name] ===");
        
        // Test case 1
        // Test case 2
        // etc.
    }
}
```

## 🧪 **Testing Individual Programs**

### **Create Test Files**
For each program, create a corresponding test file in the test directory:

**Example: TwoSumTest.java**
```java
package com.dsa.practice.arrays;

import org.junit.jupiter.api.Test;
import static org.assertj.core.api.Assertions.*;

public class TwoSumTest {
    
    @Test
    void testTwoSum_BasicCase() {
        int[] nums = {2, 7, 11, 15};
        int target = 9;
        int[] expected = {0, 1};
        
        int[] result = TwoSum.twoSum(nums, target);
        
        assertThat(result).containsExactly(expected);
    }
    
    @Test
    void testTwoSum_NoSolution() {
        int[] nums = {1, 2, 3};
        int target = 7;
        
        int[] result = TwoSum.twoSum(nums, target);
        
        assertThat(result).isEmpty();
    }
}
```

### **Run Tests**
```bash
# Run all tests
mvn test

# Run tests for specific class
mvn test -Dtest=TwoSumTest

# Run tests for specific package
mvn test -Dtest="com.dsa.practice.arrays.*"
```

## 📚 **Example Programs by Topic**

### **Arrays**
- `TwoSum.java` - Find two numbers that sum to target
- `MaxSubarray.java` - Maximum subarray sum (Kadane's algorithm)
- `MergeSortedArrays.java` - Merge two sorted arrays

### **Linked Lists**
- `ReverseLinkedList.java` - Reverse a singly linked list
- `DetectCycle.java` - Detect cycle in linked list
- `MergeTwoLists.java` - Merge two sorted linked lists

### **Stacks**
- `ValidParentheses.java` - Check for valid parentheses
- `MinStack.java` - Stack with min operation in O(1)

### **Trees**
- `BinaryTreeTraversal.java` - Inorder, preorder, postorder traversals
- `MaxDepth.java` - Maximum depth of binary tree

## 🔧 **Useful Maven Commands**

```bash
# Clean and compile
mvn clean compile

# Run specific program with clean compile
mvn clean compile exec:java -Dexec.mainClass="com.dsa.practice.arrays.TwoSum"

# Package the project
mvn package

# Run tests with verbose output
mvn test -Dtest=TwoSumTest -Dmaven.test.failure.ignore=true

# Skip tests and just compile
mvn compile -DskipTests
```

## 💡 **Best Practices**

1. **File Naming**: Use descriptive class names (e.g., `TwoSum`, `ValidParentheses`)
2. **Package Structure**: Always use the correct package declaration
3. **Main Method**: Include a main method with test cases for easy execution
4. **Documentation**: Add problem description and complexity analysis
5. **Test Coverage**: Write unit tests for each solution method
6. **Modular Design**: Keep each program independent and self-contained

## 🎯 **Quick Start Example**

1. **Create a new program:**
   ```bash
   # Navigate to appropriate folder and create file
   touch src/main/java/com/dsa/practice/arrays/TwoSum.java
   ```

2. **Add the program code** (use template above)

3. **Compile and run:**
   ```bash
   mvn compile exec:java -Dexec.mainClass="com.dsa.practice.arrays.TwoSum"
   ```

4. **Create and run tests:**
   ```bash
   touch src/test/java/com/dsa/practice/arrays/TwoSumTest.java
   # Add test code
   mvn test -Dtest=TwoSumTest
   ```

This modular approach allows you to focus on individual problems while maintaining a clean, organized project structure!
