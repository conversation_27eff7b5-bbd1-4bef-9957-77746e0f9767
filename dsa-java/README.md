# DSA Practice in Java

A comprehensive Java project for practicing Data Structures and Algorithms (DSA) with modern tooling and best practices.

## 🚀 Getting Started

### Prerequisites
- Java 21 (OpenJDK)
- Maven 3.6+

### Project Structure
```
src/
├── main/java/com/dsa/practice/
│   ├── arrays/              # Array-based problems and solutions
│   ├── linkedlists/         # Linked list implementations
│   ├── stacks/              # Stack data structure and problems
│   ├── queues/              # Queue data structure and problems
│   ├── trees/               # Tree data structures (Binary, BST, etc.)
│   ├── graphs/              # Graph algorithms and implementations
│   ├── sorting/             # Sorting algorithms
│   ├── searching/           # Searching algorithms
│   ├── dynamicprogramming/  # Dynamic programming problems
│   ├── greedy/              # Greedy algorithm problems
│   ├── backtracking/        # Backtracking problems
│   └── utils/               # Utility classes and helpers
└── test/java/com/dsa/practice/  # Corresponding test files
```

## 🛠️ Technologies Used

- **Java 21**: Latest LTS version with modern language features
- **Maven**: Build automation and dependency management
- **JUnit 5**: Modern testing framework
- **AssertJ**: Fluent assertion library for better test readability
- **Mockito**: Mocking framework for unit tests
- **Apache Commons Collections**: Additional data structures
- **Google Guava**: Utility libraries

## 📋 Available Commands

### Compile the project
```bash
mvn compile
```

### Run tests
```bash
mvn test
```

### Run the main application
```bash
mvn exec:java
```

### Clean and rebuild
```bash
mvn clean compile
```

### Package the application
```bash
mvn package
```

## 📚 Example Problems

### Arrays
- **Two Sum**: Find two numbers that add up to a target value
- More problems to be added...

### Testing
Each implementation includes comprehensive unit tests using JUnit 5 and AssertJ for better test readability.

## 🎯 Learning Goals

This project is designed to help you:
- Master fundamental data structures and algorithms
- Practice problem-solving techniques
- Learn Java best practices and modern features
- Understand time and space complexity analysis
- Write clean, testable code
- Use modern development tools and frameworks

## 📖 How to Use

1. **Study the problem**: Read the problem description and understand the requirements
2. **Analyze approaches**: Look at different solution approaches (brute force, optimized)
3. **Implement solutions**: Write your own implementation
4. **Run tests**: Verify your solution with the provided test cases
5. **Analyze complexity**: Understand time and space complexity of your solution

## 🤝 Contributing

Feel free to add more problems and solutions! Follow the existing structure and include:
- Clear problem description
- Multiple solution approaches when applicable
- Comprehensive unit tests
- Time and space complexity analysis

## 📝 Notes

- All solutions include detailed comments explaining the approach
- Test cases cover edge cases and different scenarios
- Code follows Java naming conventions and best practices
- Each package focuses on a specific category of problems

Happy coding! 🎉
