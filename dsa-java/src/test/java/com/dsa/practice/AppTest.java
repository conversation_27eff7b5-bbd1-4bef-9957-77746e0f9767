package com.dsa.practice;

import org.junit.jupiter.api.Test;
import static org.assertj.core.api.Assertions.*;

/**
 * Unit test for simple App.
 */
public class AppTest {

    @Test
    void testAppMainMethod() {
        // Test that the main method runs without throwing exceptions
        assertThatCode(() -> App.main(new String[]{}))
            .doesNotThrowAnyException();
    }

    @Test
    void testBasicAssertion() {
        // Simple test to verify testing framework is working
        assertThat(true).isTrue();
        assertThat("Hello World").isNotEmpty();
        assertThat(42).isEqualTo(42);
    }
}
