package com.dsa.practice.arrays;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Two Sum Problem
 *
 * Given an array of integers nums and an integer target, return indices of the two numbers
 * such that they add up to target.
 *
 * Example:
 * Input: nums = [2,7,11,15], target = 9
 * Output: [0,1]
 *
 * Time Complexity: O(n)
 * Space Complexity: O(n)
 */
public class TwoSum {

    /**
     * Hash map approach - O(n) time, O(n) space
     */
    public static int[] twoSum(int[] nums, int target) {
        Map<Integer, Integer> map = new HashMap<>();

        for (int i = 0; i < nums.length; i++) {
            int complement = target - nums[i];
            if (map.containsKey(complement)) {
                return new int[]{map.get(complement), i};
            }
            map.put(nums[i], i);
        }

        return new int[]{}; // No solution found
    }

    public static void main(String[] args) {
        System.out.println("=== Two Sum Problem ===");

        // Test case 1
        int[] nums1 = {2, 7, 11, 15};
        int target1 = 9;
        System.out.println("\nTest Case 1:");
        System.out.println("Input: nums = " + Arrays.toString(nums1) + ", target = " + target1);
        System.out.println("Output: " + Arrays.toString(twoSum(nums1, target1)));

        // Test case 2
        int[] nums2 = {3, 2, 4};
        int target2 = 6;
        System.out.println("\nTest Case 2:");
        System.out.println("Input: nums = " + Arrays.toString(nums2) + ", target = " + target2);
        System.out.println("Output: " + Arrays.toString(twoSum(nums2, target2)));

        // Test case 3
        int[] nums3 = {3, 3};
        int target3 = 6;
        System.out.println("\nTest Case 3:");
        System.out.println("Input: nums = " + Arrays.toString(nums3) + ", target = " + target3);
        System.out.println("Output: " + Arrays.toString(twoSum(nums3, target3)));
    }
}