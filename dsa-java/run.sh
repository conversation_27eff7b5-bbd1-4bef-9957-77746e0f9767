#!/bin/bash

# Simple script to compile and run Java DSA programs
# Usage: ./run.sh <topic> <ProgramName>
# Example: ./run.sh arrays TwoSum

if [ $# -ne 2 ]; then
    echo "Usage: ./run.sh <topic> <ProgramName>"
    echo "Example: ./run.sh arrays TwoSum"
    echo ""
    echo "Available topics:"
    echo "  arrays, linkedlists, stacks, queues, trees, graphs"
    echo "  sorting, searching, dynamicprogramming, greedy, backtracking"
    exit 1
fi

TOPIC=$1
PROGRAM=$2
JAVA_FILE="src/main/java/com/dsa/practice/$TOPIC/$PROGRAM.java"
CLASS_PATH="com.dsa.practice.$TOPIC.$PROGRAM"

# Check if file exists
if [ ! -f "$JAVA_FILE" ]; then
    echo "Error: File $JAVA_FILE not found!"
    exit 1
fi

echo "🔨 Compiling $PROGRAM.java..."
cd src/main/java
javac com/dsa/practice/$TOPIC/$PROGRAM.java

if [ $? -eq 0 ]; then
    echo "✅ Compilation successful!"
    echo "🚀 Running $PROGRAM..."
    echo "----------------------------------------"
    java $CLASS_PATH
    echo "----------------------------------------"
    echo "✅ Execution completed!"
else
    echo "❌ Compilation failed!"
    exit 1
fi
